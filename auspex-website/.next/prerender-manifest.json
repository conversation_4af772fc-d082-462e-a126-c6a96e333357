{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/live-sets": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/live-sets", "dataRoute": "/live-sets.rsc"}, "/releases": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/releases", "dataRoute": "/releases.rsc"}, "/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico/"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "d5dd73284070565601a62a785633e7c2", "previewModeSigningKey": "5e0608b2a51bae099d43dd69b412e68d5a304c3d38db572429dcaf4e79b8168c", "previewModeEncryptionKey": "2f1ef4f9974836fd3c3bcc551ee8c10924eb13d4073d3c345ed5a99fab90d77f"}}