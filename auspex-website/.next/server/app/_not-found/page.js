(()=>{var e={};e.id=409,e.ids=[409],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},7925:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>f,originalPathname:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l}),s(7352),s(5866),s(9977);var r=s(3191),a=s(8716),n=s(7922),o=s.n(n),i=s(5231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let l=["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,9977)),"/Users/<USER>/Workspace/auspex/auspex-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],u=[],c="/_not-found/page",f={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2328:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},5261:(e,t,s)=>{Promise.resolve().then(s.bind(s,2534)),Promise.resolve().then(s.bind(s,422))},645:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(326);function a(){return r.jsx("footer",{className:"bg-transparent",children:r.jsx("div",{className:"container mx-auto px-4 py-6",children:r.jsx("div",{className:"text-center text-white/40 text-sm",children:(0,r.jsxs)("p",{children:["\xa9 ",new Date().getFullYear()," Auspex Records. All rights reserved."]})})})})}},2445:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var r=s(326),a=s(434),n=s(5047),o=s(1223),i=s(58);function d(){let e=(0,n.usePathname)();return r.jsx(i.E.header,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},transition:{duration:.5,ease:"easeOut"},className:"fixed top-0 left-0 right-0 z-50",children:r.jsx("div",{className:"container mx-auto flex h-20 items-center justify-center px-6",children:r.jsx("nav",{className:"flex items-center gap-2 rounded-full p-2 bg-black/30 backdrop-blur-lg border border-white/10 shadow-lg",children:[{href:"/",label:"Home"},{href:"/releases",label:"Releases"},{href:"/live-sets",label:"Live Sets"}].map(t=>(0,r.jsxs)(a.default,{href:t.href,className:(0,o.cn)("relative text-md font-medium transition-colors text-white/80 hover:text-white px-4 py-2 rounded-full"),children:[t.label,(e===t.href||e===t.href+"/")&&r.jsx(i.E.div,{className:"absolute inset-0 bg-primary/20 rounded-full -z-10",layoutId:"active-link",transition:{type:"spring",stiffness:500,damping:30,mass:.8}})]},t.href))})})})}},2534:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(326),a=s(5047),n=s(2445),o=s(645);function i({children:e}){let t="/"===(0,a.usePathname)();return(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"aurora-bg"}),(0,r.jsxs)("div",{className:"relative flex min-h-screen w-full flex-col",children:[!t&&r.jsx(n.Z,{}),r.jsx("main",{className:"flex-1",children:e}),!t&&r.jsx(o.Z,{})]})]})}},422:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>h});var r=s(326),a=s(4097),n=s(7577),o=s(4559),i=s(9360),d=s(3020),l=s(1223);let u=o.zt,c=n.forwardRef(({className:e,...t},s)=>r.jsx(o.l_,{ref:s,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=o.l_.displayName;let f=(0,i.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=n.forwardRef(({className:e,variant:t,...s},a)=>r.jsx(o.fC,{ref:a,className:(0,l.cn)(f({variant:t}),e),...s}));p.displayName=o.fC.displayName,n.forwardRef(({className:e,...t},s)=>r.jsx(o.aU,{ref:s,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=o.aU.displayName;let m=n.forwardRef(({className:e,...t},s)=>r.jsx(o.x8,{ref:s,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:r.jsx(d.Z,{className:"h-4 w-4"})}));m.displayName=o.x8.displayName;let x=n.forwardRef(({className:e,...t},s)=>r.jsx(o.Dx,{ref:s,className:(0,l.cn)("text-sm font-semibold",e),...t}));x.displayName=o.Dx.displayName;let v=n.forwardRef(({className:e,...t},s)=>r.jsx(o.dk,{ref:s,className:(0,l.cn)("text-sm opacity-90",e),...t}));function h(){let{toasts:e}=(0,a.pm)();return(0,r.jsxs)(u,{children:[e.map(function({id:e,title:t,description:s,action:a,...n}){return(0,r.jsxs)(p,{...n,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&r.jsx(x,{children:t}),s&&r.jsx(v,{children:s})]}),a,r.jsx(m,{})]},e)}),r.jsx(c,{})]})}v.displayName=o.dk.displayName},4097:(e,t,s)=>{"use strict";s.d(t,{pm:()=>f});var r=s(7577);let a=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function u(e){l=i(l,e),d.forEach(e=>{e(l)})}function c({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=r.useState(l);return r.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},1223:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(1135),a=s(1009);function n(...e){return(0,a.m6)((0,r.W)(e))}},6399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{isNotFoundError:function(){return a},notFound:function(){return r}});let s="NEXT_NOT_FOUND";function r(){let e=Error(s);throw e.digest=s,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===s}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7352:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return a},default:function(){return n}});let r=s(6399),a="next/dist/client/components/parallel-route-default.js";function n(){(0,r.notFound)()}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9977:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>i});var r=s(9510);s(5023);var a=s(8570);let n=(0,a.createProxy)(String.raw`/Users/<USER>/Workspace/auspex/auspex-website/src/components/ui/toaster.tsx#Toaster`),o=(0,a.createProxy)(String.raw`/Users/<USER>/Workspace/auspex/auspex-website/src/components/layout/layout-client.tsx#default`),i={title:"Auspex Records",description:"Independent label committed to curating and cultivating future-facing sound."};function d({children:e}){return r.jsx("html",{lang:"en",className:"dark",children:(0,r.jsxs)("body",{className:"antialiased",children:[r.jsx(o,{children:e}),r.jsx(n,{})]})})}},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[948,864],()=>s(7925));module.exports=r})();